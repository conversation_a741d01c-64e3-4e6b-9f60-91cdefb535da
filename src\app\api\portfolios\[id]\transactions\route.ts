import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import {
  getPortfolioById,
  getPortfolioTransactionsWithAssets,
} from "@/utils/db/portfolio-queries";

// GET /api/portfolios/[id]/transactions - Get transactions for a specific portfolio
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { id } = await params;
    const portfolioId = id;

    if (!portfolioId) {
      return NextResponse.json(
        { error: "ID-ul portofoliului este obligatoriu" },
        { status: 400 }
      );
    }

    // Verify that the portfolio exists and belongs to the user
    const portfolio = await getPortfolioById(portfolioId);
    if (!portfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să accesezi acest portofoliu" },
        { status: 403 }
      );
    }

    // Get query parameters for pagination
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get("limit");
    const offsetParam = searchParams.get("offset");

    const limit = limitParam ? parseInt(limitParam, 10) : undefined;
    const offset = offsetParam ? parseInt(offsetParam, 10) : undefined;

    // Validate pagination parameters
    if (limit !== undefined && (isNaN(limit) || limit < 1 || limit > 100)) {
      return NextResponse.json(
        { error: "Parametrul 'limit' trebuie să fie între 1 și 100" },
        { status: 400 }
      );
    }

    if (offset !== undefined && (isNaN(offset) || offset < 0)) {
      return NextResponse.json(
        { error: "Parametrul 'offset' trebuie să fie un număr pozitiv" },
        { status: 400 }
      );
    }

    // Get transactions with asset information
    const transactions = await getPortfolioTransactionsWithAssets(
      portfolioId,
      limit,
      offset
    );

    return NextResponse.json({
      transactions,
      count: transactions.length,
      portfolio: {
        id: portfolio.id,
        name: portfolio.name,
        description: portfolio.description,
      },
      pagination: {
        limit: limit || null,
        offset: offset || 0,
        hasMore: limit ? transactions.length === limit : false,
      },
      message: "Tranzacțiile au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolio transactions:", error);
    return NextResponse.json(
      { error: "Nu s-au putut încărca tranzacțiile" },
      { status: 500 }
    );
  }
}
