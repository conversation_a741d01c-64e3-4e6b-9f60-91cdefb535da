"use client";

import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Edit,
  Trash2,
  MoreVertical,
  TrendingUp,
  TrendingDown,
  Building2,
} from "lucide-react";
import {
  formatTransactionDate,
  formatQuantity,
} from "@/lib/transaction-schemas";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface TransactionRowProps {
  transaction: TransactionWithAssetInfo;
  onEdit: (transaction: TransactionWithAssetInfo) => void;
  onDelete: (transaction: TransactionWithAssetInfo) => void;
  variant?: "table" | "card";
  className?: string;
}

export function TransactionRow({
  transaction,
  onEdit,
  onDelete,
  variant = "table",
  className,
}: TransactionRowProps) {
  const handleEdit = () => {
    onEdit(transaction);
  };

  const handleDelete = () => {
    onDelete(transaction);
  };

  // Calculate total value
  const totalValue = transaction.latest_price?.close_price
    ? transaction.quantity * transaction.latest_price.close_price
    : null;

  // Format price
  const formatPrice = (price: number) => {
    return price.toLocaleString("ro-RO", {
      style: "currency",
      currency: "RON",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const ActionButtons = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreVertical className="h-4 w-4" />
          <span className="sr-only">Deschide meniul</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleEdit} className="cursor-pointer">
          <Edit className="mr-2 h-4 w-4" />
          Editează
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleDelete}
          className="cursor-pointer text-red-600 dark:text-red-400 dark:hover:text-red-700 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Șterge
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  if (variant === "card") {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-md bg-muted">
                  {transaction.asset?.logo_url ? (
                    <Image
                      src={transaction.asset.logo_url}
                      alt={transaction.ticker}
                      className="h-6 w-6"
                      width={24}
                      height={24}
                    />
                  ) : (
                    <Building2 className="h-6 w-6 text-muted-foreground" />
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-lg">
                      {transaction.ticker}
                    </span>
                    <Badge
                      variant={
                        transaction.transaction_type === "BUY"
                          ? "default"
                          : "destructive"
                      }
                      className="text-xs"
                    >
                      {transaction.transaction_type === "BUY" ? (
                        <>
                          <TrendingUp className="h-3 w-3 mr-1" />
                          CUMPĂRARE
                        </>
                      ) : (
                        <>
                          <TrendingDown className="h-3 w-3 mr-1" />
                          VÂNZARE
                        </>
                      )}
                    </Badge>
                  </div>
                  {transaction.asset && (
                    <p className="text-sm text-muted-foreground">
                      {transaction.asset.name}
                    </p>
                  )}
                </div>
              </div>
            </div>
            <ActionButtons />
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Cantitate</p>
              <p className="font-medium">
                {formatQuantity(transaction.quantity)}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Preț actual</p>
              <p className="font-medium">
                {transaction.latest_price?.close_price
                  ? formatPrice(transaction.latest_price.close_price)
                  : "N/A"}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Valoare totală</p>
              <p className="font-medium">
                {totalValue ? formatPrice(totalValue) : "N/A"}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Data tranzacției</p>
              <p className="font-medium">
                {formatTransactionDate(transaction.transaction_date)}
              </p>
            </div>
          </div>

          {transaction.notes && (
            <div className="mt-3 pt-3 border-t">
              <p className="text-muted-foreground text-xs">Note</p>
              <p className="text-sm">{transaction.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Table row variant
  return (
    <tr
      className={cn("border-b hover:bg-muted/50 transition-colors", className)}
    >
      <td className="p-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-md bg-muted">
            {transaction.asset?.logo_url ? (
              <Image
                src={transaction.asset.logo_url}
                alt={transaction.ticker}
                className="h-6 w-6"
                width={24}
                height={24}
              />
            ) : (
              <Building2 className="h-6 w-6 text-muted-foreground" />
            )}
          </div>
          <div>
            <div className="font-semibold">{transaction.ticker}</div>
            {transaction.asset && (
              <div className="text-sm text-muted-foreground">
                {transaction.asset.name}
              </div>
            )}
          </div>
        </div>
      </td>
      <td className="p-4">
        <Badge
          variant={
            transaction.transaction_type === "BUY" ? "default" : "destructive"
          }
          className="text-xs"
        >
          {transaction.transaction_type === "BUY" ? (
            <>
              <TrendingUp className="h-3 w-3 mr-1" />
              CUMPĂRARE
            </>
          ) : (
            <>
              <TrendingDown className="h-3 w-3 mr-1" />
              VÂNZARE
            </>
          )}
        </Badge>
      </td>
      <td className="p-4 text-right font-medium">
        {formatQuantity(transaction.quantity)}
      </td>
      <td className="p-4 text-right font-medium">
        {transaction.latest_price?.close_price
          ? formatPrice(transaction.latest_price.close_price)
          : "N/A"}
      </td>
      <td className="p-4 text-right font-medium">
        {totalValue ? formatPrice(totalValue) : "N/A"}
      </td>
      <td className="p-4">
        <div>
          <div className="font-medium">
            {formatTransactionDate(transaction.transaction_date)}
          </div>
          {transaction.notes && (
            <div
              className="text-sm text-muted-foreground mt-1"
              title={transaction.notes}
            >
              {transaction.notes.length > 30
                ? `${transaction.notes.substring(0, 30)}...`
                : transaction.notes}
            </div>
          )}
        </div>
      </td>
      <td className="p-4 text-center">
        <ActionButtons />
      </td>
    </tr>
  );
}
