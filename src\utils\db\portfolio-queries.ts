import { hasuraQuery, hasuraMutation } from "./hasura";

// Types
export interface Portfolio {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  portfolio_id: string;
  ticker: string;
  quantity: number;
  transaction_date: string;
  transaction_type: "BUY" | "SELL";
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface AssetPrice {
  price_id: number;
  asset_id: number;
  date: string;
  open_price: number;
  high_price: number;
  low_price: number;
  close_price: number;
  adj_close: number;
  volume: number;
  created_at: string;
  updated_at: string;
}

export interface TransactionWithAssetInfo {
  id: string;
  portfolio_id: string;
  ticker: string;
  quantity: number;
  transaction_date: string;
  transaction_type: "BUY" | "SELL";
  notes?: string;
  created_at: string;
  updated_at: string;
  asset?: {
    asset_id: number;
    ticker: string;
    name: string;
    company: string;
    logo_url?: string;
  };
  latest_price?: {
    close_price: number;
    date: string;
  };
}

// GraphQL Queries

/**
 * Get all active portfolios for a user
 */
export const GET_USER_PORTFOLIOS = `
  query GetUserPortfolios($userId: String!) {
    ptvuser_portfolios(
      where: {
        user_id: {_eq: $userId}
        is_active: {_eq: true}
      }
      order_by: {created_at: asc}
    ) {
      id
      user_id
      name
      description
      is_active
      created_at
      updated_at
    }
  }
`;

/**
 * Get a specific portfolio by ID
 */
export const GET_PORTFOLIO_BY_ID = `
  query GetPortfolioById($portfolioId: uuid!) {
    ptvuser_portfolios_by_pk(id: $portfolioId) {
      id
      user_id
      name
      description
      is_active
      created_at
      updated_at
    }
  }
`;

/**
 * Get transactions for a portfolio
 */
export const GET_PORTFOLIO_TRANSACTIONS = `
  query GetPortfolioTransactions($portfolioId: uuid!, $limit: Int, $offset: Int) {
    ptvuser_transactions(
      where: {portfolio_id: {_eq: $portfolioId}}
      order_by: {transaction_date: desc, created_at: desc}
      limit: $limit
      offset: $offset
    ) {
      id
      portfolio_id
      ticker
      quantity
      transaction_date
      transaction_type
      notes
      created_at
      updated_at
    }
  }
`;

/**
 * Get transactions for a portfolio with asset information and latest prices
 */
export const GET_PORTFOLIO_TRANSACTIONS_WITH_ASSETS = `
  query GetPortfolioTransactionsWithAssets($portfolioId: uuid!, $limit: Int, $offset: Int) {
    ptvuser_transactions(
      where: {portfolio_id: {_eq: $portfolioId}}
      order_by: {transaction_date: desc, created_at: desc}
      limit: $limit
      offset: $offset
    ) {
      id
      portfolio_id
      ticker
      quantity
      transaction_date
      transaction_type
      notes
      created_at
      updated_at
    }
  }
`;

/**
 * Get a single transaction by ID
 */
export const GET_TRANSACTION_BY_ID = `
  query GetTransactionById($transactionId: uuid!) {
    ptvuser_transactions_by_pk(id: $transactionId) {
      id
      portfolio_id
      ticker
      quantity
      transaction_date
      transaction_type
      notes
      created_at
      updated_at
    }
  }
`;

/**
 * Get latest asset price by ticker
 */
export const GET_LATEST_ASSET_PRICE_BY_TICKER = `
  query GetLatestAssetPriceByTicker($ticker: String!) {
    ptvuser_asset(where: {ticker: {_eq: $ticker}}, limit: 1) {
      asset_id
      ticker
      name
      company
      logo_url
    }
  }
`;

/**
 * Get latest asset price by asset_id
 */
export const GET_LATEST_ASSET_PRICE_BY_ASSET_ID = `
  query GetLatestAssetPriceByAssetId($assetId: Int!) {
    ptvuser_asset_price(
      where: {asset_id: {_eq: $assetId}}
      order_by: {date: desc}
      limit: 1
    ) {
      close_price
      date
      asset_id
    }
  }
`;

// GraphQL Mutations

/**
 * Create a new portfolio
 */
export const CREATE_PORTFOLIO = `
  mutation CreatePortfolio($object: ptvuser_portfolios_insert_input!) {
    insert_ptvuser_portfolios_one(object: $object) {
      id
      user_id
      name
      description
      is_active
      created_at
      updated_at
    }
  }
`;

/**
 * Create a new transaction
 */
export const CREATE_TRANSACTION = `
  mutation CreateTransaction($object: ptvuser_transactions_insert_input!) {
    insert_ptvuser_transactions_one(object: $object) {
      id
      portfolio_id
      ticker
      quantity
      transaction_date
      transaction_type
      notes
      created_at
      updated_at
    }
  }
`;

/**
 * Update a transaction
 */
export const UPDATE_TRANSACTION = `
  mutation UpdateTransaction($transactionId: uuid!, $updates: ptvuser_transactions_set_input!) {
    update_ptvuser_transactions_by_pk(
      pk_columns: {id: $transactionId}
      _set: $updates
    ) {
      id
      portfolio_id
      ticker
      quantity
      transaction_date
      transaction_type
      notes
      created_at
      updated_at
    }
  }
`;

/**
 * Delete a transaction
 */
export const DELETE_TRANSACTION = `
  mutation DeleteTransaction($transactionId: uuid!) {
    delete_ptvuser_transactions_by_pk(id: $transactionId) {
      id
      ticker
      quantity
      transaction_date
      transaction_type
    }
  }
`;

/**
 * Update a portfolio
 */
export const UPDATE_PORTFOLIO = `
  mutation UpdatePortfolio($portfolioId: uuid!, $updates: ptvuser_portfolios_set_input!) {
    update_ptvuser_portfolios_by_pk(pk_columns: {id: $portfolioId}, _set: $updates) {
      id
      user_id
      name
      description
      is_active
      created_at
      updated_at
    }
  }
`;

// Utility Functions

/**
 * Get all active portfolios for a user
 */
export async function getUserPortfolios(userId: string): Promise<Portfolio[]> {
  try {
    const result = await hasuraQuery<{
      ptvuser_portfolios: Portfolio[];
    }>(GET_USER_PORTFOLIOS, { variables: { userId } });

    return result.ptvuser_portfolios || [];
  } catch (error) {
    console.error("Error fetching user portfolios:", error);
    throw new Error("Nu s-au putut încărca portofoliile");
  }
}

/**
 * Get a specific portfolio by ID
 */
export async function getPortfolioById(
  portfolioId: string
): Promise<Portfolio | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_portfolios_by_pk: Portfolio | null;
    }>(GET_PORTFOLIO_BY_ID, { variables: { portfolioId } });

    return result.ptvuser_portfolios_by_pk;
  } catch (error) {
    console.error("Error fetching portfolio:", error);
    throw new Error("Nu s-a putut încărca portofoliul");
  }
}

/**
 * Create a default portfolio for a user
 */
export async function createDefaultPortfolio(
  userId: string
): Promise<Portfolio> {
  try {
    const result = await hasuraMutation<{
      insert_ptvuser_portfolios_one: Portfolio;
    }>(CREATE_PORTFOLIO, {
      variables: {
        object: {
          user_id: userId,
          name: "Portofoliul Meu",
          description: "Portofoliul principal",
          is_active: true,
        },
      },
    });

    return result.insert_ptvuser_portfolios_one;
  } catch (error) {
    console.error("Error creating default portfolio:", error);
    throw new Error("Nu s-a putut crea portofoliul implicit");
  }
}

/**
 * Create a new portfolio
 */
export async function createPortfolio(
  userId: string,
  name: string,
  description?: string
): Promise<Portfolio> {
  try {
    const result = await hasuraMutation<{
      insert_ptvuser_portfolios_one: Portfolio;
    }>(CREATE_PORTFOLIO, {
      variables: {
        object: {
          user_id: userId,
          name,
          description,
          is_active: true,
        },
      },
    });

    return result.insert_ptvuser_portfolios_one;
  } catch (error) {
    console.error("Error creating portfolio:", error);
    throw new Error("Nu s-a putut crea portofoliul");
  }
}

/**
 * Update a portfolio
 */
export async function updatePortfolio(
  portfolioId: string,
  updates: {
    name?: string;
    description?: string | null;
    is_active?: boolean;
  }
): Promise<Portfolio> {
  try {
    const result = await hasuraMutation<{
      update_ptvuser_portfolios_by_pk: Portfolio;
    }>(UPDATE_PORTFOLIO, {
      variables: {
        portfolioId,
        updates,
      },
    });

    if (!result.update_ptvuser_portfolios_by_pk) {
      throw new Error("Portofoliul nu a fost găsit");
    }

    return result.update_ptvuser_portfolios_by_pk;
  } catch (error) {
    console.error("Error updating portfolio:", error);
    if (
      error instanceof Error &&
      error.message === "Portofoliul nu a fost găsit"
    ) {
      throw error;
    }
    throw new Error("Nu s-a putut actualiza portofoliul");
  }
}

/**
 * Get transactions for a portfolio
 */
export async function getPortfolioTransactions(
  portfolioId: string,
  limit?: number,
  offset?: number
): Promise<Transaction[]> {
  try {
    const result = await hasuraQuery<{
      ptvuser_transactions: Transaction[];
    }>(GET_PORTFOLIO_TRANSACTIONS, {
      variables: { portfolioId, limit, offset },
    });

    return result.ptvuser_transactions || [];
  } catch (error) {
    console.error("Error fetching portfolio transactions:", error);
    throw new Error("Nu s-au putut încărca tranzacțiile");
  }
}

/**
 * Get transactions for a portfolio with asset information
 */
export async function getPortfolioTransactionsWithAssets(
  portfolioId: string,
  limit?: number,
  offset?: number
): Promise<TransactionWithAssetInfo[]> {
  try {
    // First get the transactions
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: Transaction[];
    }>(GET_PORTFOLIO_TRANSACTIONS, {
      variables: { portfolioId, limit, offset },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];

    // For each transaction, get asset info and latest price
    const transactionsWithAssets: TransactionWithAssetInfo[] =
      await Promise.all(
        transactions.map(async (transaction) => {
          try {
            // First get asset info
            const assetResult = await hasuraQuery<{
              ptvuser_asset: Array<{
                asset_id: number;
                ticker: string;
                name: string;
                company: string;
                logo_url?: string;
              }>;
            }>(GET_LATEST_ASSET_PRICE_BY_TICKER, {
              variables: { ticker: transaction.ticker },
            });

            const asset = assetResult.ptvuser_asset?.[0];
            let latestPrice = undefined;

            // If asset exists, get latest price
            if (asset) {
              try {
                const priceResult = await hasuraQuery<{
                  ptvuser_asset_price: Array<{
                    close_price: number;
                    date: string;
                    asset_id: number;
                  }>;
                }>(GET_LATEST_ASSET_PRICE_BY_ASSET_ID, {
                  variables: { assetId: asset.asset_id },
                });

                const price = priceResult.ptvuser_asset_price?.[0];
                if (price) {
                  latestPrice = {
                    close_price: price.close_price,
                    date: price.date,
                  };
                }
              } catch (priceError) {
                console.error(
                  `Error fetching price for asset ${asset.asset_id}:`,
                  priceError
                );
              }
            }

            console.log("Transaction with asset info:", {
              ...transaction,
              asset: asset
                ? {
                    asset_id: asset.asset_id,
                    ticker: asset.ticker,
                    name: asset.name,
                    company: asset.company,
                    logo_url: asset.logo_url,
                  }
                : undefined,
              latest_price: latestPrice,
            });

            return {
              ...transaction,
              asset: asset
                ? {
                    asset_id: asset.asset_id,
                    ticker: asset.ticker,
                    name: asset.name,
                    company: asset.company,
                    logo_url: asset.logo_url,
                  }
                : undefined,
              latest_price: latestPrice,
            };
          } catch (error) {
            console.error(
              `Error fetching asset info for ${transaction.ticker}:`,
              error
            );
            return {
              ...transaction,
              asset: undefined,
              latest_price: undefined,
            };
          }
        })
      );

    return transactionsWithAssets;
  } catch (error) {
    console.error("Error fetching portfolio transactions with assets:", error);
    throw new Error("Nu s-au putut încărca tranzacțiile");
  }
}

/**
 * Get a single transaction by ID
 */
export async function getTransactionById(
  transactionId: string
): Promise<Transaction | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_transactions_by_pk: Transaction | null;
    }>(GET_TRANSACTION_BY_ID, {
      variables: { transactionId },
    });

    return result.ptvuser_transactions_by_pk;
  } catch (error) {
    console.error("Error fetching transaction:", error);
    throw new Error("Nu s-a putut încărca tranzacția");
  }
}

/**
 * Create a new transaction
 */
export async function createTransaction(
  portfolioId: string,
  ticker: string,
  quantity: number,
  transactionDate: string,
  transactionType: "BUY" | "SELL" = "BUY",
  notes?: string
): Promise<Transaction> {
  try {
    const result = await hasuraMutation<{
      insert_ptvuser_transactions_one: Transaction;
    }>(CREATE_TRANSACTION, {
      variables: {
        object: {
          portfolio_id: portfolioId,
          ticker: ticker.toUpperCase(),
          quantity,
          transaction_date: transactionDate,
          transaction_type: transactionType,
          notes,
        },
      },
    });

    return result.insert_ptvuser_transactions_one;
  } catch (error) {
    console.error("Error creating transaction:", error);
    throw new Error("Nu s-a putut crea tranzacția");
  }
}

/**
 * Update a transaction
 */
export async function updateTransaction(
  transactionId: string,
  updates: {
    ticker?: string;
    quantity?: number;
    transaction_date?: string;
    transaction_type?: "BUY" | "SELL";
    notes?: string;
  }
): Promise<Transaction> {
  try {
    // Prepare the updates object, ensuring ticker is uppercase if provided
    const updateObject: any = {};
    if (updates.ticker !== undefined) {
      updateObject.ticker = updates.ticker.toUpperCase();
    }
    if (updates.quantity !== undefined) {
      updateObject.quantity = updates.quantity;
    }
    if (updates.transaction_date !== undefined) {
      updateObject.transaction_date = updates.transaction_date;
    }
    if (updates.transaction_type !== undefined) {
      updateObject.transaction_type = updates.transaction_type;
    }
    if (updates.notes !== undefined) {
      updateObject.notes = updates.notes;
    }

    const result = await hasuraMutation<{
      update_ptvuser_transactions_by_pk: Transaction;
    }>(UPDATE_TRANSACTION, {
      variables: {
        transactionId,
        updates: updateObject,
      },
    });

    if (!result.update_ptvuser_transactions_by_pk) {
      throw new Error("Tranzacția nu a fost găsită");
    }

    return result.update_ptvuser_transactions_by_pk;
  } catch (error) {
    console.error("Error updating transaction:", error);
    if (
      error instanceof Error &&
      error.message === "Tranzacția nu a fost găsită"
    ) {
      throw error;
    }
    throw new Error("Nu s-a putut actualiza tranzacția");
  }
}

/**
 * Delete a transaction
 */
export async function deleteTransaction(transactionId: string): Promise<void> {
  try {
    const result = await hasuraMutation<{
      delete_ptvuser_transactions_by_pk: {
        id: string;
        ticker: string;
        quantity: number;
        transaction_date: string;
        transaction_type: string;
      } | null;
    }>(DELETE_TRANSACTION, {
      variables: { transactionId },
    });

    if (!result.delete_ptvuser_transactions_by_pk) {
      throw new Error("Tranzacția nu a fost găsită");
    }
  } catch (error) {
    console.error("Error deleting transaction:", error);
    if (
      error instanceof Error &&
      error.message === "Tranzacția nu a fost găsită"
    ) {
      throw error;
    }
    throw new Error("Nu s-a putut șterge tranzacția");
  }
}

/**
 * Get or create a default portfolio for a user
 * If user has no portfolios, creates a default one
 * Returns the user's portfolios and indicates if a default was created
 */
export async function ensureUserHasPortfolio(userId: string): Promise<{
  portfolios: Portfolio[];
  defaultCreated: boolean;
}> {
  try {
    let portfolios = await getUserPortfolios(userId);
    let defaultCreated = false;

    if (portfolios.length === 0) {
      const defaultPortfolio = await createDefaultPortfolio(userId);
      portfolios = [defaultPortfolio];
      defaultCreated = true;
    }

    return { portfolios, defaultCreated };
  } catch (error) {
    console.error("Error ensuring user has portfolio:", error);
    throw new Error("Nu s-a putut verifica portofoliul utilizatorului");
  }
}

// Portfolio Metrics Types
export interface PortfolioHolding {
  ticker: string;
  totalQuantity: number;
  totalBuyQuantity: number;
  totalSellQuantity: number;
  transactionCount: number;
  firstTransactionDate: string;
  lastTransactionDate: string;
}

export interface PortfolioMetrics {
  totalHoldings: number;
  uniqueTickers: number;
  totalTransactions: number;
  holdings: PortfolioHolding[];
  lastTransactionDate?: string;
  firstTransactionDate?: string;
}

export interface PortfolioWithMetrics extends Portfolio {
  metrics: PortfolioMetrics;
}

/**
 * Calculate portfolio metrics from transactions
 */
export function calculatePortfolioMetrics(
  transactions: Transaction[]
): PortfolioMetrics {
  if (transactions.length === 0) {
    return {
      totalHoldings: 0,
      uniqueTickers: 0,
      totalTransactions: 0,
      holdings: [],
    };
  }

  // Group transactions by ticker
  const holdingsMap = new Map<string, PortfolioHolding>();

  let firstTransactionDate: string | undefined;
  let lastTransactionDate: string | undefined;

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker.toUpperCase();

    // Track overall date range
    if (
      !firstTransactionDate ||
      transaction.transaction_date < firstTransactionDate
    ) {
      firstTransactionDate = transaction.transaction_date;
    }
    if (
      !lastTransactionDate ||
      transaction.transaction_date > lastTransactionDate
    ) {
      lastTransactionDate = transaction.transaction_date;
    }

    if (!holdingsMap.has(ticker)) {
      holdingsMap.set(ticker, {
        ticker,
        totalQuantity: 0,
        totalBuyQuantity: 0,
        totalSellQuantity: 0,
        transactionCount: 0,
        firstTransactionDate: transaction.transaction_date,
        lastTransactionDate: transaction.transaction_date,
      });
    }

    const holding = holdingsMap.get(ticker)!;

    // Update holding data
    holding.transactionCount++;

    if (transaction.transaction_type === "BUY") {
      holding.totalBuyQuantity += transaction.quantity;
      holding.totalQuantity += transaction.quantity;
    } else if (transaction.transaction_type === "SELL") {
      holding.totalSellQuantity += transaction.quantity;
      holding.totalQuantity -= transaction.quantity;
    }

    // Update date range for this holding
    if (transaction.transaction_date < holding.firstTransactionDate) {
      holding.firstTransactionDate = transaction.transaction_date;
    }
    if (transaction.transaction_date > holding.lastTransactionDate) {
      holding.lastTransactionDate = transaction.transaction_date;
    }
  });

  const holdings = Array.from(holdingsMap.values());

  // Filter out holdings with zero quantity (fully sold positions)
  const activeHoldings = holdings.filter(
    (holding) => holding.totalQuantity > 0
  );

  return {
    totalHoldings: activeHoldings.length,
    uniqueTickers: holdings.length,
    totalTransactions: transactions.length,
    holdings: holdings.sort((a, b) => b.totalQuantity - a.totalQuantity), // Sort by quantity descending
    firstTransactionDate,
    lastTransactionDate,
  };
}

/**
 * Get all active portfolios for a user with calculated metrics
 */
export async function getUserPortfoliosWithMetrics(
  userId: string
): Promise<PortfolioWithMetrics[]> {
  try {
    const portfolios = await getUserPortfolios(userId);

    // Get metrics for each portfolio
    const portfoliosWithMetrics = await Promise.all(
      portfolios.map(async (portfolio) => {
        const transactions = await getPortfolioTransactions(portfolio.id);
        const metrics = calculatePortfolioMetrics(transactions);

        return {
          ...portfolio,
          metrics,
        };
      })
    );

    return portfoliosWithMetrics;
  } catch (error) {
    console.error("Error fetching user portfolios with metrics:", error);
    throw new Error("Nu s-au putut încărca portofoliile cu metrici");
  }
}
