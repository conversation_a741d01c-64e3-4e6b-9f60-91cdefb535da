"use client";

import { AddTransactionModal } from "@/components/transactions/add-transaction-modal";
import { TransactionsList } from "@/components/transactions/transactions-list";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Portfolio,
  TransactionWithAssetInfo,
} from "@/utils/db/portfolio-queries";
import { AlertCircle, ArrowLeft, PieChart } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

interface PortfolioDetailClientProps {
  portfolio: Portfolio;
  className?: string;
}

export function PortfolioDetailClient({
  portfolio,
  className,
}: PortfolioDetailClientProps) {
  const router = useRouter();
  const [transactions, setTransactions] = useState<TransactionWithAssetInfo[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const loadTransactions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `/api/portfolios/${portfolio.id}/transactions`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Nu s-au putut încărca tranzacțiile"
        );
      }

      const data = await response.json();
      setTransactions(data.transactions || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [portfolio.id]);

  useEffect(() => {
    loadTransactions();
  }, [portfolio.id, loadTransactions]);

  const handleAddTransaction = () => {
    setIsModalOpen(true);
  };

  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);
  };

  const handleModalSuccess = () => {
    setIsModalOpen(false);
    loadTransactions();
  };

  const handleTransactionUpdated = () => {
    loadTransactions();
  };

  const handleTransactionDeleted = () => {
    loadTransactions();
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/portfolios">Portofolii</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{portfolio.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-start gap-12">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Înapoi
            </Button>
            <div>
              <div className="flex items-center gap-3">
                <PieChart className="h-8 w-8 text-portavio-blue" />
                <h1 className="text-3xl font-bold text-foreground">
                  {portfolio.name}
                </h1>
                <Badge variant="secondary" className="text-xs">
                  {portfolio.is_active ? "Activ" : "Inactiv"}
                </Badge>
              </div>
              {portfolio.description && (
                <p className="text-muted-foreground mt-2">
                  {portfolio.description}
                </p>
              )}
            </div>
          </div>
          {/* <div className="flex items-center gap-3">
            <Button onClick={handleAddTransaction} className="gap-2">
              <Plus className="h-4 w-4" />
              Adaugă tranzacție
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link
                    href={`/portfolios/${portfolio.id}/edit`}
                    className="cursor-pointer"
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Editează portofoliul
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div> */}
        </div>
        {/* Portfolio Metrics
        <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-green-500/10">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <p className="text-2xl font-bold">
                    {totalValue.toLocaleString("ro-RO", {
                      style: "currency",
                      currency: "RON",
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Valoare totală
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-portavio-blue/10">
                  <Building2 className="h-5 w-5 text-portavio-blue" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{uniqueAssets}</p>
                  <p className="text-sm text-muted-foreground">Active unice</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-portavio-orange/10">
                  <Calendar className="h-5 w-5 text-portavio-orange" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{totalTransactions}</p>
                  <p className="text-sm text-muted-foreground">
                    Total tranzacții
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}
        {error ? (
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  A apărut o eroare
                </h3>
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button onClick={loadTransactions} variant="outline">
                  Încearcă din nou
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <TransactionsList
            transactions={transactions}
            portfolioId={portfolio.id}
            portfolioName={portfolio.name}
            onAddTransaction={handleAddTransaction}
            onTransactionUpdated={handleTransactionUpdated}
            onTransactionDeleted={handleTransactionDeleted}
            isLoading={isLoading}
          />
        )}
      </div>

      {/* Add Transaction Modal */}
      <AddTransactionModal
        open={isModalOpen}
        onOpenChange={handleModalOpenChange}
        onSuccess={handleModalSuccess}
        defaultPortfolioId={portfolio.id}
      />
    </div>
  );
}
