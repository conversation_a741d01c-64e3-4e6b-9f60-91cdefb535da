"use client";

import { useState } from "react";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { formatTransactionDate, formatQuantity } from "@/lib/transaction-schemas";
import { toast } from "sonner";
import { Loader2, AlertTriangle, TrendingUp, TrendingDown } from "lucide-react";

interface DeleteTransactionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: TransactionWithAssetInfo;
  onSuccess?: () => void;
}

export function DeleteTransactionDialog({
  open,
  onOpenChange,
  transaction,
  onSuccess,
}: DeleteTransactionDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);

      const response = await fetch(`/api/transactions/${transaction.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nu s-a putut șterge tranzacția");
      }

      toast.success("Tranzacția a fost ștearsă cu succes!");
      onOpenChange(false);
      onSuccess?.();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !isDeleting) {
      onOpenChange(newOpen);
    }
  };

  // Calculate total value
  const totalValue = transaction.latest_price?.close_price 
    ? transaction.quantity * transaction.latest_price.close_price 
    : null;

  // Format price
  const formatPrice = (price: number) => {
    return price.toLocaleString('ro-RO', { 
      style: 'currency', 
      currency: 'RON',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2 
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Șterge tranzacția
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p>
                Ești sigur că vrei să ștergi această tranzacție? Această acțiune nu poate fi anulată.
              </p>
              
              {/* Transaction Details */}
              <div className="bg-muted rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-lg">{transaction.ticker}</span>
                    <Badge 
                      variant={transaction.transaction_type === "BUY" ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {transaction.transaction_type === "BUY" ? (
                        <>
                          <TrendingUp className="h-3 w-3 mr-1" />
                          CUMPĂRARE
                        </>
                      ) : (
                        <>
                          <TrendingDown className="h-3 w-3 mr-1" />
                          VÂNZARE
                        </>
                      )}
                    </Badge>
                  </div>
                </div>

                {transaction.asset && (
                  <p className="text-sm text-muted-foreground">
                    {transaction.asset.name}
                  </p>
                )}

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <p className="text-muted-foreground">Cantitate</p>
                    <p className="font-medium">{formatQuantity(transaction.quantity)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Data</p>
                    <p className="font-medium">{formatTransactionDate(transaction.transaction_date)}</p>
                  </div>
                  {transaction.latest_price?.close_price && (
                    <>
                      <div>
                        <p className="text-muted-foreground">Preț actual</p>
                        <p className="font-medium">{formatPrice(transaction.latest_price.close_price)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Valoare totală</p>
                        <p className="font-medium">{totalValue ? formatPrice(totalValue) : "N/A"}</p>
                      </div>
                    </>
                  )}
                </div>

                {transaction.notes && (
                  <div className="pt-2 border-t">
                    <p className="text-muted-foreground text-xs">Note</p>
                    <p className="text-sm">{transaction.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            Anulează
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Se șterge...
              </>
            ) : (
              "Șterge tranzacția"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
